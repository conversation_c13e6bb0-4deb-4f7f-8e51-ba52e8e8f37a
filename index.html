<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>GameHub Arena - Ultimate Gaming Experience</title>
    <link rel="stylesheet" href="styles.css">
    <link href="https://fonts.googleapis.com/css2?family=Orbitron:wght@400;700;900&family=Rajdhani:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
</head>
<body>
    <!-- Navigation -->
    <nav class="navbar">
        <div class="nav-container">
            <div class="nav-logo">
                <i class="fas fa-gamepad"></i>
                <span>GameHub Arena</span>
            </div>
            <ul class="nav-menu">
                <li><a href="#home" class="nav-link">Home</a></li>
                <li><a href="#hall" class="nav-link">Gaming Hall</a></li>
                <li><a href="#about" class="nav-link">About</a></li>
                <li><a href="#contact" class="nav-link">Contact</a></li>
            </ul>
            <div class="hamburger">
                <span class="bar"></span>
                <span class="bar"></span>
                <span class="bar"></span>
            </div>
        </div>
    </nav>

    <!-- Hero Section -->
    <section id="home" class="hero">
        <div class="hero-background">
            <div class="hero-particles"></div>
        </div>
        <div class="hero-content">
            <h1 class="hero-title">
                <span class="glitch" data-text="GAMEHUB">GAMEHUB</span>
                <span class="arena-text">ARENA</span>
            </h1>
            <p class="hero-subtitle">Experience the Future of Gaming</p>
            <div class="hero-stats">
                <div class="stat">
                    <span class="stat-number">20</span>
                    <span class="stat-label">PC Stations</span>
                </div>
                <div class="stat">
                    <span class="stat-number">5</span>
                    <span class="stat-label">PS5 Consoles</span>
                </div>
                <div class="stat">
                    <span class="stat-number">24/7</span>
                    <span class="stat-label">Open</span>
                </div>
            </div>
            <button class="cta-button" onclick="scrollToHall()">
                <span>Reserve Your Station</span>
                <i class="fas fa-arrow-down"></i>
            </button>
        </div>
    </section>

    <!-- Gaming Hall Section -->
    <section id="hall" class="gaming-hall">
        <div class="container">
            <h2 class="section-title">Interactive Gaming Hall</h2>
            <p class="section-subtitle">Click on any station to make a reservation</p>
            
            <!-- Status Legend -->
            <div class="status-legend">
                <div class="legend-item">
                    <div class="status-indicator available"></div>
                    <span>Available</span>
                </div>
                <div class="legend-item">
                    <div class="status-indicator reserved"></div>
                    <span>Reserved</span>
                </div>
                <div class="legend-item">
                    <div class="status-indicator in-use-soon"></div>
                    <span>In Use Soon</span>
                </div>
            </div>

            <!-- Hall Layout Container -->
            <div class="hall-layout">
                <div class="hall-grid">
                    <!-- PC Stations will be generated by JavaScript -->
                    <div class="pc-section">
                        <h3 class="section-label">PC Gaming Stations</h3>
                        <div id="pc-stations" class="stations-grid pc-grid"></div>
                    </div>
                    
                    <!-- PS5 Stations -->
                    <div class="ps5-section">
                        <h3 class="section-label">PlayStation 5 Consoles</h3>
                        <div id="ps5-stations" class="stations-grid ps5-grid"></div>
                    </div>
                </div>
            </div>

            <!-- Real-time Status Bar -->
            <div class="status-bar">
                <div class="status-info">
                    <span id="available-count">25</span> Available | 
                    <span id="reserved-count">0</span> Reserved | 
                    <span id="in-use-count">0</span> In Use Soon
                </div>
                <div class="last-updated">
                    Last updated: <span id="last-updated-time">Just now</span>
                </div>
            </div>
        </div>
    </section>

    <!-- Reservation Modal -->
    <div id="reservation-modal" class="modal">
        <div class="modal-content">
            <div class="modal-header">
                <h3 id="modal-title">Reserve Gaming Station</h3>
                <span class="close-modal">&times;</span>
            </div>
            <div class="modal-body">
                <div class="selected-station-info">
                    <div class="station-preview">
                        <div id="selected-station-icon"></div>
                        <div class="station-details">
                            <h4 id="selected-station-name">PC Station 1</h4>
                            <p id="selected-station-specs">High-end gaming PC</p>
                        </div>
                    </div>
                </div>
                
                <form id="reservation-form" class="reservation-form">
                    <div class="form-group">
                        <label for="customer-name">Full Name *</label>
                        <input type="text" id="customer-name" name="name" required>
                        <span class="error-message"></span>
                    </div>
                    
                    <div class="form-group">
                        <label for="customer-email">Email Address *</label>
                        <input type="email" id="customer-email" name="email" required>
                        <span class="error-message"></span>
                    </div>
                    
                    <div class="form-group">
                        <label for="customer-phone">Phone Number *</label>
                        <input type="tel" id="customer-phone" name="phone" required>
                        <span class="error-message"></span>
                    </div>
                    
                    <div class="form-row">
                        <div class="form-group">
                            <label for="reservation-date">Date *</label>
                            <input type="date" id="reservation-date" name="date" required>
                            <span class="error-message"></span>
                        </div>
                        
                        <div class="form-group">
                            <label for="start-time">Start Time *</label>
                            <input type="time" id="start-time" name="startTime" required>
                            <span class="error-message"></span>
                        </div>
                        
                        <div class="form-group">
                            <label for="end-time">End Time *</label>
                            <input type="time" id="end-time" name="endTime" required>
                            <span class="error-message"></span>
                        </div>
                    </div>
                    
                    <div class="form-actions">
                        <button type="button" class="btn-cancel" onclick="closeModal()">Cancel</button>
                        <button type="submit" class="btn-reserve">
                            <i class="fas fa-calendar-check"></i>
                            Confirm Reservation
                        </button>
                    </div>
                </form>
            </div>
        </div>
    </div>

    <!-- Confirmation Modal -->
    <div id="confirmation-modal" class="modal">
        <div class="modal-content confirmation-content">
            <div class="confirmation-header">
                <div class="success-icon">
                    <i class="fas fa-check-circle"></i>
                </div>
                <h3>Reservation Confirmed!</h3>
            </div>
            <div class="confirmation-body">
                <div id="confirmation-details"></div>
                <button class="btn-close-confirmation" onclick="closeConfirmationModal()">
                    <i class="fas fa-times"></i>
                    Close
                </button>
            </div>
        </div>
    </div>

    <!-- Scripts -->
    <script src="script.js"></script>
</body>
</html>
