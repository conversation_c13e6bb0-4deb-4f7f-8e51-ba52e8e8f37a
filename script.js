// GameHub Arena - Interactive Gaming Hall Reservation System

// Global variables
let stations = [];
let selectedStation = null;
let reservations = JSON.parse(localStorage.getItem('reservations')) || [];

// Station configurations
const stationConfigs = {
    pc: {
        count: 20,
        icon: 'fas fa-desktop',
        name: 'PC Station',
        specs: 'High-end gaming PC with RTX 4080'
    },
    ps5: {
        count: 5,
        icon: 'fab fa-playstation',
        name: 'PlayStation 5',
        specs: 'Sony PlayStation 5 Console'
    }
};

// Initialize the application
document.addEventListener('DOMContentLoaded', function() {
    initializeStations();
    setupEventListeners();
    updateStatusBar();
    setMinDate();
    startStatusUpdater();
});

// Initialize gaming stations
function initializeStations() {
    // Create PC stations
    const pcContainer = document.getElementById('pc-stations');
    for (let i = 1; i <= stationConfigs.pc.count; i++) {
        const station = createStation('pc', i);
        stations.push(station);
        pcContainer.appendChild(station.element);
    }

    // Create PS5 stations
    const ps5Container = document.getElementById('ps5-stations');
    for (let i = 1; i <= stationConfigs.ps5.count; i++) {
        const station = createStation('ps5', i);
        stations.push(station);
        ps5Container.appendChild(station.element);
    }

    // Apply existing reservations
    applyReservations();
}

// Create a gaming station element
function createStation(type, number) {
    const config = stationConfigs[type];
    const stationId = `${type}-${number}`;
    
    const element = document.createElement('div');
    element.className = 'gaming-station available';
    element.id = stationId;
    element.innerHTML = `
        <i class="${config.icon}"></i>
        <span class="station-number">${number}</span>
    `;

    const station = {
        id: stationId,
        type: type,
        number: number,
        status: 'available',
        element: element,
        config: config
    };

    // Add click event listener
    element.addEventListener('click', () => selectStation(station));

    return station;
}

// Setup event listeners
function setupEventListeners() {
    // Modal close events
    document.querySelector('.close-modal').addEventListener('click', closeModal);
    document.getElementById('reservation-modal').addEventListener('click', function(e) {
        if (e.target === this) closeModal();
    });

    // Form submission
    document.getElementById('reservation-form').addEventListener('submit', handleReservation);

    // Navigation smooth scrolling
    document.querySelectorAll('.nav-link').forEach(link => {
        link.addEventListener('click', function(e) {
            e.preventDefault();
            const targetId = this.getAttribute('href').substring(1);
            const targetElement = document.getElementById(targetId);
            if (targetElement) {
                targetElement.scrollIntoView({ behavior: 'smooth' });
            }
        });
    });

    // Form validation on input
    const inputs = document.querySelectorAll('#reservation-form input');
    inputs.forEach(input => {
        input.addEventListener('blur', validateField);
        input.addEventListener('input', clearError);
    });
}

// Select a gaming station
function selectStation(station) {
    if (station.status !== 'available') {
        showNotification('This station is not available for reservation.', 'error');
        return;
    }

    // Remove previous selection
    if (selectedStation) {
        selectedStation.element.classList.remove('selected');
    }

    // Select new station
    selectedStation = station;
    station.element.classList.add('selected');

    // Update modal content
    updateModalContent(station);

    // Show modal
    document.getElementById('reservation-modal').style.display = 'block';
    document.body.style.overflow = 'hidden';
}

// Update modal content with selected station info
function updateModalContent(station) {
    const config = station.config;
    document.getElementById('modal-title').textContent = `Reserve ${config.name} ${station.number}`;
    document.getElementById('selected-station-name').textContent = `${config.name} ${station.number}`;
    document.getElementById('selected-station-specs').textContent = config.specs;
    
    const iconElement = document.getElementById('selected-station-icon');
    iconElement.innerHTML = `<i class="${config.icon}"></i>`;
}

// Close reservation modal
function closeModal() {
    document.getElementById('reservation-modal').style.display = 'none';
    document.body.style.overflow = 'auto';
    
    if (selectedStation) {
        selectedStation.element.classList.remove('selected');
        selectedStation = null;
    }
    
    clearForm();
}

// Handle reservation form submission
function handleReservation(e) {
    e.preventDefault();
    
    if (!validateForm()) {
        return;
    }

    const formData = new FormData(e.target);
    const reservation = {
        id: generateId(),
        stationId: selectedStation.id,
        stationType: selectedStation.type,
        stationNumber: selectedStation.number,
        customerName: formData.get('name'),
        customerEmail: formData.get('email'),
        customerPhone: formData.get('phone'),
        date: formData.get('date'),
        startTime: formData.get('startTime'),
        endTime: formData.get('endTime'),
        timestamp: new Date().toISOString()
    };

    // Save reservation
    reservations.push(reservation);
    localStorage.setItem('reservations', JSON.stringify(reservations));

    // Update station status
    updateStationStatus(selectedStation.id, 'reserved');

    // Show confirmation
    showConfirmation(reservation);

    // Close modal
    closeModal();

    // Update status bar
    updateStatusBar();
}

// Validate form
function validateForm() {
    let isValid = true;
    const requiredFields = ['name', 'email', 'phone', 'date', 'startTime', 'endTime'];
    
    requiredFields.forEach(fieldName => {
        const field = document.querySelector(`[name="${fieldName}"]`);
        if (!validateField({ target: field })) {
            isValid = false;
        }
    });

    // Validate time range
    const startTime = document.getElementById('start-time').value;
    const endTime = document.getElementById('end-time').value;
    
    if (startTime && endTime && startTime >= endTime) {
        showFieldError('end-time', 'End time must be after start time');
        isValid = false;
    }

    return isValid;
}

// Validate individual field
function validateField(e) {
    const field = e.target;
    const value = field.value.trim();
    const fieldName = field.name;
    
    clearFieldError(fieldName);
    
    if (!value) {
        showFieldError(fieldName, 'This field is required');
        return false;
    }
    
    switch (fieldName) {
        case 'email':
            const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
            if (!emailRegex.test(value)) {
                showFieldError(fieldName, 'Please enter a valid email address');
                return false;
            }
            break;
            
        case 'phone':
            const phoneRegex = /^[\+]?[1-9][\d]{0,15}$/;
            if (!phoneRegex.test(value.replace(/[\s\-\(\)]/g, ''))) {
                showFieldError(fieldName, 'Please enter a valid phone number');
                return false;
            }
            break;
            
        case 'date':
            const selectedDate = new Date(value);
            const today = new Date();
            today.setHours(0, 0, 0, 0);
            
            if (selectedDate < today) {
                showFieldError(fieldName, 'Please select a future date');
                return false;
            }
            break;
    }
    
    return true;
}

// Show field error
function showFieldError(fieldName, message) {
    const field = document.querySelector(`[name="${fieldName}"]`);
    const errorElement = field.parentNode.querySelector('.error-message');
    
    field.style.borderColor = 'var(--danger-color)';
    errorElement.textContent = message;
    errorElement.style.display = 'block';
}

// Clear field error
function clearFieldError(fieldName) {
    const field = document.querySelector(`[name="${fieldName}"]`);
    const errorElement = field.parentNode.querySelector('.error-message');
    
    field.style.borderColor = 'rgba(0, 255, 255, 0.3)';
    errorElement.style.display = 'none';
}

// Clear error on input
function clearError(e) {
    clearFieldError(e.target.name);
}

// Show confirmation modal
function showConfirmation(reservation) {
    const modal = document.getElementById('confirmation-modal');
    const detailsElement = document.getElementById('confirmation-details');
    
    const config = stationConfigs[reservation.stationType];
    
    detailsElement.innerHTML = `
        <div style="margin-bottom: 15px;">
            <strong>Station:</strong> ${config.name} ${reservation.stationNumber}
        </div>
        <div style="margin-bottom: 15px;">
            <strong>Customer:</strong> ${reservation.customerName}
        </div>
        <div style="margin-bottom: 15px;">
            <strong>Email:</strong> ${reservation.customerEmail}
        </div>
        <div style="margin-bottom: 15px;">
            <strong>Phone:</strong> ${reservation.customerPhone}
        </div>
        <div style="margin-bottom: 15px;">
            <strong>Date:</strong> ${formatDate(reservation.date)}
        </div>
        <div style="margin-bottom: 15px;">
            <strong>Time:</strong> ${reservation.startTime} - ${reservation.endTime}
        </div>
        <div style="margin-bottom: 15px;">
            <strong>Reservation ID:</strong> ${reservation.id}
        </div>
    `;
    
    modal.style.display = 'block';
}

// Close confirmation modal
function closeConfirmationModal() {
    document.getElementById('confirmation-modal').style.display = 'none';
}

// Update station status
function updateStationStatus(stationId, status) {
    const station = stations.find(s => s.id === stationId);
    if (station) {
        station.status = status;
        station.element.className = `gaming-station ${status}`;
    }
}

// Apply existing reservations
function applyReservations() {
    const now = new Date();
    
    reservations.forEach(reservation => {
        const reservationDate = new Date(reservation.date + 'T' + reservation.startTime);
        const endDate = new Date(reservation.date + 'T' + reservation.endTime);
        
        if (endDate < now) {
            // Reservation has ended, remove it
            return;
        }
        
        let status = 'reserved';
        if (reservationDate <= now && now <= endDate) {
            status = 'in-use-soon';
        }
        
        updateStationStatus(reservation.stationId, status);
    });
    
    // Clean up expired reservations
    reservations = reservations.filter(reservation => {
        const endDate = new Date(reservation.date + 'T' + reservation.endTime);
        return endDate >= now;
    });
    
    localStorage.setItem('reservations', JSON.stringify(reservations));
}

// Update status bar
function updateStatusBar() {
    const available = stations.filter(s => s.status === 'available').length;
    const reserved = stations.filter(s => s.status === 'reserved').length;
    const inUseSoon = stations.filter(s => s.status === 'in-use-soon').length;
    
    document.getElementById('available-count').textContent = available;
    document.getElementById('reserved-count').textContent = reserved;
    document.getElementById('in-use-count').textContent = inUseSoon;
    document.getElementById('last-updated-time').textContent = new Date().toLocaleTimeString();
}

// Start status updater
function startStatusUpdater() {
    setInterval(() => {
        applyReservations();
        updateStatusBar();
    }, 60000); // Update every minute
}

// Set minimum date for reservation
function setMinDate() {
    const today = new Date().toISOString().split('T')[0];
    document.getElementById('reservation-date').min = today;
}

// Clear form
function clearForm() {
    document.getElementById('reservation-form').reset();
    document.querySelectorAll('.error-message').forEach(error => {
        error.style.display = 'none';
    });
    document.querySelectorAll('#reservation-form input').forEach(input => {
        input.style.borderColor = 'rgba(0, 255, 255, 0.3)';
    });
}

// Utility functions
function generateId() {
    return 'res_' + Date.now() + '_' + Math.random().toString(36).substr(2, 9);
}

function formatDate(dateString) {
    const date = new Date(dateString);
    return date.toLocaleDateString('en-US', {
        weekday: 'long',
        year: 'numeric',
        month: 'long',
        day: 'numeric'
    });
}

function showNotification(message, type = 'info') {
    // Create notification element
    const notification = document.createElement('div');
    notification.className = `notification ${type}`;
    notification.innerHTML = `
        <div class="notification-content">
            <i class="fas fa-${type === 'error' ? 'exclamation-circle' : 'info-circle'}"></i>
            <span>${message}</span>
        </div>
    `;
    
    // Add styles
    notification.style.cssText = `
        position: fixed;
        top: 100px;
        right: 20px;
        background: ${type === 'error' ? 'var(--danger-color)' : 'var(--primary-color)'};
        color: var(--dark-bg);
        padding: 15px 20px;
        border-radius: 8px;
        z-index: 3000;
        font-weight: 600;
        box-shadow: 0 5px 20px rgba(0, 0, 0, 0.3);
        animation: slideInRight 0.3s ease;
    `;
    
    document.body.appendChild(notification);
    
    // Remove after 3 seconds
    setTimeout(() => {
        notification.style.animation = 'slideOutRight 0.3s ease';
        setTimeout(() => {
            document.body.removeChild(notification);
        }, 300);
    }, 3000);
}

// Smooth scroll to hall section
function scrollToHall() {
    document.getElementById('hall').scrollIntoView({ behavior: 'smooth' });
}

// Advanced Features and Animations

// Countdown timers for reserved stations
let countdownTimers = {};

// Add countdown timer to station
function addCountdownTimer(stationId, endTime) {
    const station = stations.find(s => s.id === stationId);
    if (!station) return;

    // Create timer element
    const timerElement = document.createElement('div');
    timerElement.className = 'countdown-timer';
    station.element.appendChild(timerElement);

    // Update timer every second
    const timerId = setInterval(() => {
        const now = new Date();
        const timeLeft = endTime - now;

        if (timeLeft <= 0) {
            clearInterval(timerId);
            station.element.removeChild(timerElement);
            delete countdownTimers[stationId];
            updateStationStatus(stationId, 'available');
            updateStatusBar();
            return;
        }

        const minutes = Math.floor(timeLeft / (1000 * 60));
        const seconds = Math.floor((timeLeft % (1000 * 60)) / 1000);
        timerElement.textContent = `${minutes}:${seconds.toString().padStart(2, '0')}`;
    }, 1000);

    countdownTimers[stationId] = timerId;
}

// Enhanced station selection with particle effects
function selectStationWithEffects(station) {
    // Create particle effect
    createParticleEffect(station.element);

    // Add selection sound effect (visual feedback)
    station.element.style.transform = 'scale(1.2)';
    setTimeout(() => {
        station.element.style.transform = '';
    }, 200);

    selectStation(station);
}

// Create particle effect
function createParticleEffect(element) {
    const rect = element.getBoundingClientRect();
    const centerX = rect.left + rect.width / 2;
    const centerY = rect.top + rect.height / 2;

    for (let i = 0; i < 8; i++) {
        const particle = document.createElement('div');
        particle.className = 'particle';
        particle.style.left = centerX + 'px';
        particle.style.top = centerY + 'px';
        particle.style.animationDelay = (i * 0.1) + 's';

        // Random direction
        const angle = (i * 45) * Math.PI / 180;
        const distance = 50;
        const endX = centerX + Math.cos(angle) * distance;
        const endY = centerY + Math.sin(angle) * distance;

        particle.style.setProperty('--end-x', endX + 'px');
        particle.style.setProperty('--end-y', endY + 'px');

        document.body.appendChild(particle);

        // Remove particle after animation
        setTimeout(() => {
            if (document.body.contains(particle)) {
                document.body.removeChild(particle);
            }
        }, 3000);
    }
}

// Enhanced reservation handling with animations
function handleReservationWithAnimation(e) {
    e.preventDefault();

    if (!validateForm()) {
        // Shake form on validation error
        const form = document.getElementById('reservation-form');
        form.style.animation = 'shake 0.5s ease-in-out';
        setTimeout(() => {
            form.style.animation = '';
        }, 500);
        return;
    }

    // Show loading state
    const submitButton = document.querySelector('.btn-reserve');
    const originalText = submitButton.innerHTML;
    submitButton.innerHTML = '<div class="loading-spinner"></div> Processing...';
    submitButton.disabled = true;

    // Simulate processing delay for better UX
    setTimeout(() => {
        const formData = new FormData(e.target);
        const reservation = {
            id: generateId(),
            stationId: selectedStation.id,
            stationType: selectedStation.type,
            stationNumber: selectedStation.number,
            customerName: formData.get('name'),
            customerEmail: formData.get('email'),
            customerPhone: formData.get('phone'),
            date: formData.get('date'),
            startTime: formData.get('startTime'),
            endTime: formData.get('endTime'),
            timestamp: new Date().toISOString()
        };

        // Save reservation
        reservations.push(reservation);
        localStorage.setItem('reservations', JSON.stringify(reservations));

        // Update station status with animation
        updateStationStatusWithAnimation(selectedStation.id, 'reserved');

        // Add countdown timer if reservation is for today
        const reservationDate = new Date(reservation.date);
        const today = new Date();
        if (reservationDate.toDateString() === today.toDateString()) {
            const endTime = new Date(reservation.date + 'T' + reservation.endTime);
            addCountdownTimer(selectedStation.id, endTime);
        }

        // Show confirmation with animation
        showConfirmationWithAnimation(reservation);

        // Reset button
        submitButton.innerHTML = originalText;
        submitButton.disabled = false;

        // Close modal
        closeModal();

        // Update status bar
        updateStatusBar();
    }, 1500);
}

// Update station status with animation
function updateStationStatusWithAnimation(stationId, status) {
    const station = stations.find(s => s.id === stationId);
    if (station) {
        station.status = status;

        // Animate status change
        station.element.style.transform = 'scale(0.8)';
        station.element.style.opacity = '0.5';

        setTimeout(() => {
            station.element.className = `gaming-station ${status}`;
            station.element.style.transform = 'scale(1.1)';
            station.element.style.opacity = '1';

            setTimeout(() => {
                station.element.style.transform = '';
            }, 300);
        }, 200);
    }
}

// Enhanced confirmation modal with animation
function showConfirmationWithAnimation(reservation) {
    const modal = document.getElementById('confirmation-modal');
    const detailsElement = document.getElementById('confirmation-details');

    const config = stationConfigs[reservation.stationType];

    detailsElement.innerHTML = `
        <div style="margin-bottom: 15px; animation: slideInLeft 0.5s ease;">
            <strong>Station:</strong> ${config.name} ${reservation.stationNumber}
        </div>
        <div style="margin-bottom: 15px; animation: slideInLeft 0.6s ease;">
            <strong>Customer:</strong> ${reservation.customerName}
        </div>
        <div style="margin-bottom: 15px; animation: slideInLeft 0.7s ease;">
            <strong>Email:</strong> ${reservation.customerEmail}
        </div>
        <div style="margin-bottom: 15px; animation: slideInLeft 0.8s ease;">
            <strong>Phone:</strong> ${reservation.customerPhone}
        </div>
        <div style="margin-bottom: 15px; animation: slideInLeft 0.9s ease;">
            <strong>Date:</strong> ${formatDate(reservation.date)}
        </div>
        <div style="margin-bottom: 15px; animation: slideInLeft 1s ease;">
            <strong>Time:</strong> ${reservation.startTime} - ${reservation.endTime}
        </div>
        <div style="margin-bottom: 15px; animation: slideInLeft 1.1s ease;">
            <strong>Reservation ID:</strong> ${reservation.id}
        </div>
    `;

    modal.style.display = 'block';

    // Trigger confetti effect
    createConfettiEffect();
}

// Create confetti effect
function createConfettiEffect() {
    const colors = ['#00ffff', '#ff00ff', '#ffff00', '#00ff00', '#ff0040'];

    for (let i = 0; i < 50; i++) {
        const confetti = document.createElement('div');
        confetti.style.cssText = `
            position: fixed;
            width: 10px;
            height: 10px;
            background: ${colors[Math.floor(Math.random() * colors.length)]};
            top: -10px;
            left: ${Math.random() * 100}vw;
            z-index: 3000;
            animation: confettiFall ${2 + Math.random() * 3}s linear forwards;
            transform: rotate(${Math.random() * 360}deg);
        `;

        document.body.appendChild(confetti);

        setTimeout(() => {
            if (document.body.contains(confetti)) {
                document.body.removeChild(confetti);
            }
        }, 5000);
    }
}

// Update event listeners to use enhanced functions
function setupEnhancedEventListeners() {
    // Replace form submission handler
    document.getElementById('reservation-form').removeEventListener('submit', handleReservation);
    document.getElementById('reservation-form').addEventListener('submit', handleReservationWithAnimation);

    // Update station click handlers
    stations.forEach(station => {
        station.element.removeEventListener('click', () => selectStation(station));
        station.element.addEventListener('click', () => selectStationWithEffects(station));
    });
}

// Add CSS animations for notifications and new effects
const style = document.createElement('style');
style.textContent = `
    @keyframes slideInRight {
        from { transform: translateX(100%); opacity: 0; }
        to { transform: translateX(0); opacity: 1; }
    }

    @keyframes slideOutRight {
        from { transform: translateX(0); opacity: 1; }
        to { transform: translateX(100%); opacity: 0; }
    }

    @keyframes slideInLeft {
        from { transform: translateX(-30px); opacity: 0; }
        to { transform: translateX(0); opacity: 1; }
    }

    @keyframes shake {
        0%, 100% { transform: translateX(0); }
        10%, 30%, 50%, 70%, 90% { transform: translateX(-5px); }
        20%, 40%, 60%, 80% { transform: translateX(5px); }
    }

    @keyframes confettiFall {
        0% { transform: translateY(-10px) rotate(0deg); opacity: 1; }
        100% { transform: translateY(100vh) rotate(720deg); opacity: 0; }
    }

    .notification-content {
        display: flex;
        align-items: center;
        gap: 10px;
    }

    .particle {
        animation: particleExplode 1s ease-out forwards;
    }

    @keyframes particleExplode {
        0% {
            transform: translate(0, 0) scale(1);
            opacity: 1;
        }
        100% {
            transform: translate(var(--end-x, 0), var(--end-y, 0)) scale(0);
            opacity: 0;
        }
    }
`;
document.head.appendChild(style);

// Initialize enhanced features
document.addEventListener('DOMContentLoaded', function() {
    // Wait for initial setup to complete
    setTimeout(() => {
        setupEnhancedEventListeners();
    }, 100);
});
