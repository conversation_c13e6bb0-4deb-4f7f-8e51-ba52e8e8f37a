/* Reset and Base Styles */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

:root {
    --primary-color: #00ffff;
    --secondary-color: #ff00ff;
    --accent-color: #ffff00;
    --success-color: #00ff00;
    --warning-color: #ff8800;
    --danger-color: #ff0040;
    --dark-bg: #0a0a0a;
    --card-bg: #1a1a2e;
    --text-primary: #ffffff;
    --text-secondary: #b0b0b0;
    --border-glow: #00ffff;
    --gradient-primary: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    --gradient-secondary: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
    --gradient-neon: linear-gradient(45deg, #00ffff, #ff00ff, #ffff00);
}

body {
    font-family: 'Rajdhani', sans-serif;
    background: var(--dark-bg);
    color: var(--text-primary);
    line-height: 1.6;
    overflow-x: hidden;
}

.container {
    max-width: 1400px;
    margin: 0 auto;
    padding: 0 20px;
}

/* Navigation */
.navbar {
    position: fixed;
    top: 0;
    width: 100%;
    background: rgba(10, 10, 10, 0.95);
    backdrop-filter: blur(10px);
    z-index: 1000;
    border-bottom: 1px solid var(--primary-color);
}

.nav-container {
    max-width: 1400px;
    margin: 0 auto;
    padding: 0 20px;
    display: flex;
    justify-content: space-between;
    align-items: center;
    height: 70px;
}

.nav-logo {
    display: flex;
    align-items: center;
    font-family: 'Orbitron', monospace;
    font-size: 1.5rem;
    font-weight: 700;
    color: var(--primary-color);
    text-shadow: 0 0 10px var(--primary-color);
}

.nav-logo i {
    margin-right: 10px;
    font-size: 1.8rem;
}

.nav-menu {
    display: flex;
    list-style: none;
    gap: 30px;
}

.nav-link {
    color: var(--text-primary);
    text-decoration: none;
    font-weight: 500;
    transition: all 0.3s ease;
    position: relative;
}

.nav-link:hover {
    color: var(--primary-color);
    text-shadow: 0 0 5px var(--primary-color);
}

.nav-link::after {
    content: '';
    position: absolute;
    bottom: -5px;
    left: 0;
    width: 0;
    height: 2px;
    background: var(--primary-color);
    transition: width 0.3s ease;
}

.nav-link:hover::after {
    width: 100%;
}

/* Hero Section */
.hero {
    height: 100vh;
    display: flex;
    align-items: center;
    justify-content: center;
    position: relative;
    background: radial-gradient(circle at center, #1a1a2e 0%, #0a0a0a 100%);
    overflow: hidden;
}

.hero-background {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="grid" width="10" height="10" patternUnits="userSpaceOnUse"><path d="M 10 0 L 0 0 0 10" fill="none" stroke="%2300ffff" stroke-width="0.5" opacity="0.3"/></pattern></defs><rect width="100" height="100" fill="url(%23grid)"/></svg>');
    animation: gridMove 20s linear infinite;
}

@keyframes gridMove {
    0% { transform: translate(0, 0); }
    100% { transform: translate(10px, 10px); }
}

.hero-particles {
    position: absolute;
    width: 100%;
    height: 100%;
    background: transparent;
}

.hero-particles::before {
    content: '';
    position: absolute;
    top: 20%;
    left: 10%;
    width: 4px;
    height: 4px;
    background: var(--primary-color);
    border-radius: 50%;
    box-shadow: 
        100px 200px var(--secondary-color),
        200px 100px var(--accent-color),
        300px 300px var(--primary-color),
        400px 150px var(--secondary-color),
        500px 250px var(--accent-color);
    animation: float 6s ease-in-out infinite;
}

@keyframes float {
    0%, 100% { transform: translateY(0px); }
    50% { transform: translateY(-20px); }
}

.hero-content {
    text-align: center;
    z-index: 2;
}

.hero-title {
    font-family: 'Orbitron', monospace;
    font-size: 4rem;
    font-weight: 900;
    margin-bottom: 20px;
    display: flex;
    flex-direction: column;
    align-items: center;
}

.glitch {
    position: relative;
    color: var(--primary-color);
    text-shadow: 0 0 20px var(--primary-color);
    animation: glitch 2s infinite;
}

.glitch::before,
.glitch::after {
    content: attr(data-text);
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
}

.glitch::before {
    animation: glitch-1 0.5s infinite;
    color: var(--secondary-color);
    z-index: -1;
}

.glitch::after {
    animation: glitch-2 0.5s infinite;
    color: var(--accent-color);
    z-index: -2;
}

@keyframes glitch {
    0%, 100% { transform: translate(0); }
    20% { transform: translate(-2px, 2px); }
    40% { transform: translate(-2px, -2px); }
    60% { transform: translate(2px, 2px); }
    80% { transform: translate(2px, -2px); }
}

@keyframes glitch-1 {
    0%, 100% { transform: translate(0); }
    10% { transform: translate(-2px, -2px); }
    20% { transform: translate(2px, 2px); }
    30% { transform: translate(-2px, 2px); }
    40% { transform: translate(2px, -2px); }
}

@keyframes glitch-2 {
    0%, 100% { transform: translate(0); }
    10% { transform: translate(2px, 2px); }
    20% { transform: translate(-2px, -2px); }
    30% { transform: translate(2px, -2px); }
    40% { transform: translate(-2px, 2px); }
}

.arena-text {
    color: var(--secondary-color);
    text-shadow: 0 0 20px var(--secondary-color);
    margin-top: 10px;
}

.hero-subtitle {
    font-size: 1.5rem;
    color: var(--text-secondary);
    margin-bottom: 40px;
    font-weight: 300;
}

.hero-stats {
    display: flex;
    justify-content: center;
    gap: 60px;
    margin-bottom: 40px;
}

.stat {
    text-align: center;
}

.stat-number {
    display: block;
    font-family: 'Orbitron', monospace;
    font-size: 2.5rem;
    font-weight: 700;
    color: var(--primary-color);
    text-shadow: 0 0 10px var(--primary-color);
}

.stat-label {
    font-size: 1rem;
    color: var(--text-secondary);
    text-transform: uppercase;
    letter-spacing: 1px;
}

.cta-button {
    background: linear-gradient(45deg, var(--primary-color), var(--secondary-color));
    border: none;
    padding: 15px 40px;
    font-size: 1.2rem;
    font-weight: 600;
    color: var(--dark-bg);
    border-radius: 50px;
    cursor: pointer;
    transition: all 0.3s ease;
    display: flex;
    align-items: center;
    gap: 10px;
    margin: 0 auto;
    text-transform: uppercase;
    letter-spacing: 1px;
}

.cta-button:hover {
    transform: translateY(-3px);
    box-shadow: 0 10px 30px rgba(0, 255, 255, 0.3);
}

.cta-button i {
    animation: bounce 2s infinite;
}

@keyframes bounce {
    0%, 100% { transform: translateY(0); }
    50% { transform: translateY(-5px); }
}

/* Gaming Hall Section */
.gaming-hall {
    padding: 100px 0;
    background: linear-gradient(135deg, #0a0a0a 0%, #1a1a2e 50%, #0a0a0a 100%);
}

.section-title {
    font-family: 'Orbitron', monospace;
    font-size: 3rem;
    text-align: center;
    margin-bottom: 20px;
    color: var(--primary-color);
    text-shadow: 0 0 20px var(--primary-color);
}

.section-subtitle {
    text-align: center;
    font-size: 1.2rem;
    color: var(--text-secondary);
    margin-bottom: 50px;
}

/* Status Legend */
.status-legend {
    display: flex;
    justify-content: center;
    gap: 40px;
    margin-bottom: 40px;
    flex-wrap: wrap;
}

.legend-item {
    display: flex;
    align-items: center;
    gap: 10px;
    font-weight: 500;
}

.status-indicator {
    width: 20px;
    height: 20px;
    border-radius: 50%;
    border: 2px solid transparent;
}

.status-indicator.available {
    background: var(--success-color);
    box-shadow: 0 0 10px var(--success-color);
}

.status-indicator.reserved {
    background: var(--danger-color);
    box-shadow: 0 0 10px var(--danger-color);
}

.status-indicator.in-use-soon {
    background: var(--warning-color);
    box-shadow: 0 0 10px var(--warning-color);
}

/* Hall Layout */
.hall-layout {
    background: rgba(26, 26, 46, 0.5);
    border: 2px solid var(--primary-color);
    border-radius: 20px;
    padding: 40px;
    margin-bottom: 40px;
    backdrop-filter: blur(10px);
}

.hall-grid {
    display: grid;
    gap: 40px;
    grid-template-columns: 2fr 1fr;
}

.section-label {
    font-family: 'Orbitron', monospace;
    font-size: 1.5rem;
    color: var(--secondary-color);
    text-align: center;
    margin-bottom: 30px;
    text-shadow: 0 0 10px var(--secondary-color);
}

.stations-grid {
    display: grid;
    gap: 15px;
    justify-items: center;
}

.pc-grid {
    grid-template-columns: repeat(5, 1fr);
}

.ps5-grid {
    grid-template-columns: repeat(2, 1fr);
    max-width: 300px;
    margin: 0 auto;
}

/* Gaming Station Styles */
.gaming-station {
    width: 80px;
    height: 80px;
    border-radius: 15px;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    transition: all 0.3s ease;
    position: relative;
    border: 2px solid transparent;
    background: rgba(255, 255, 255, 0.1);
    backdrop-filter: blur(5px);
}

.gaming-station i {
    font-size: 2rem;
    margin-bottom: 5px;
}

.station-number {
    font-size: 0.8rem;
    font-weight: 600;
    font-family: 'Orbitron', monospace;
}

.gaming-station.available {
    border-color: var(--success-color);
    color: var(--success-color);
    box-shadow: 0 0 15px rgba(0, 255, 0, 0.3);
}

.gaming-station.reserved {
    border-color: var(--danger-color);
    color: var(--danger-color);
    box-shadow: 0 0 15px rgba(255, 0, 64, 0.3);
}

.gaming-station.in-use-soon {
    border-color: var(--warning-color);
    color: var(--warning-color);
    box-shadow: 0 0 15px rgba(255, 136, 0, 0.3);
}

.gaming-station:hover {
    transform: scale(1.1);
    box-shadow: 0 0 25px currentColor;
}

.gaming-station.selected {
    transform: scale(1.2);
    animation: pulse 1s infinite;
}

@keyframes pulse {
    0%, 100% { box-shadow: 0 0 25px currentColor; }
    50% { box-shadow: 0 0 40px currentColor; }
}

/* Status Bar */
.status-bar {
    display: flex;
    justify-content: space-between;
    align-items: center;
    background: rgba(26, 26, 46, 0.8);
    padding: 20px;
    border-radius: 10px;
    border: 1px solid var(--primary-color);
    font-family: 'Orbitron', monospace;
}

.status-info {
    font-size: 1.1rem;
    font-weight: 600;
}

.last-updated {
    color: var(--text-secondary);
    font-size: 0.9rem;
}

/* Modal Styles */
.modal {
    display: none;
    position: fixed;
    z-index: 2000;
    left: 0;
    top: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(0, 0, 0, 0.8);
    backdrop-filter: blur(5px);
}

.modal-content {
    background: var(--card-bg);
    margin: 5% auto;
    padding: 0;
    border: 2px solid var(--primary-color);
    border-radius: 20px;
    width: 90%;
    max-width: 600px;
    animation: modalSlideIn 0.3s ease;
    box-shadow: 0 0 50px rgba(0, 255, 255, 0.3);
}

@keyframes modalSlideIn {
    from { transform: translateY(-50px); opacity: 0; }
    to { transform: translateY(0); opacity: 1; }
}

.modal-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 25px 30px;
    border-bottom: 1px solid var(--primary-color);
    background: linear-gradient(135deg, rgba(0, 255, 255, 0.1), rgba(255, 0, 255, 0.1));
}

.modal-header h3 {
    font-family: 'Orbitron', monospace;
    font-size: 1.5rem;
    color: var(--primary-color);
    text-shadow: 0 0 10px var(--primary-color);
}

.close-modal {
    color: var(--text-secondary);
    font-size: 2rem;
    font-weight: bold;
    cursor: pointer;
    transition: color 0.3s ease;
}

.close-modal:hover {
    color: var(--danger-color);
    text-shadow: 0 0 10px var(--danger-color);
}

.modal-body {
    padding: 30px;
}

/* Selected Station Info */
.selected-station-info {
    margin-bottom: 30px;
}

.station-preview {
    display: flex;
    align-items: center;
    gap: 20px;
    background: rgba(255, 255, 255, 0.05);
    padding: 20px;
    border-radius: 15px;
    border: 1px solid var(--primary-color);
}

#selected-station-icon {
    width: 60px;
    height: 60px;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 2.5rem;
    border-radius: 10px;
    border: 2px solid var(--success-color);
    color: var(--success-color);
    box-shadow: 0 0 15px rgba(0, 255, 0, 0.3);
}

.station-details h4 {
    font-family: 'Orbitron', monospace;
    font-size: 1.3rem;
    color: var(--primary-color);
    margin-bottom: 5px;
}

.station-details p {
    color: var(--text-secondary);
    font-size: 1rem;
}

/* Form Styles */
.reservation-form {
    display: grid;
    gap: 20px;
}

.form-group {
    display: flex;
    flex-direction: column;
}

.form-row {
    display: grid;
    grid-template-columns: 1fr 1fr 1fr;
    gap: 20px;
}

.form-group label {
    font-weight: 600;
    margin-bottom: 8px;
    color: var(--text-primary);
    font-size: 1rem;
}

.form-group input {
    padding: 12px 15px;
    border: 2px solid rgba(0, 255, 255, 0.3);
    border-radius: 8px;
    background: rgba(255, 255, 255, 0.05);
    color: var(--text-primary);
    font-size: 1rem;
    transition: all 0.3s ease;
}

.form-group input:focus {
    outline: none;
    border-color: var(--primary-color);
    box-shadow: 0 0 10px rgba(0, 255, 255, 0.3);
}

.error-message {
    color: var(--danger-color);
    font-size: 0.9rem;
    margin-top: 5px;
    display: none;
}

.form-actions {
    display: flex;
    gap: 15px;
    justify-content: flex-end;
    margin-top: 20px;
}

.btn-cancel,
.btn-reserve {
    padding: 12px 25px;
    border: none;
    border-radius: 8px;
    font-size: 1rem;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.3s ease;
    display: flex;
    align-items: center;
    gap: 8px;
}

.btn-cancel {
    background: rgba(255, 255, 255, 0.1);
    color: var(--text-secondary);
    border: 1px solid rgba(255, 255, 255, 0.2);
}

.btn-cancel:hover {
    background: rgba(255, 255, 255, 0.2);
    color: var(--text-primary);
}

.btn-reserve {
    background: linear-gradient(45deg, var(--primary-color), var(--secondary-color));
    color: var(--dark-bg);
    font-weight: 700;
}

.btn-reserve:hover {
    transform: translateY(-2px);
    box-shadow: 0 5px 20px rgba(0, 255, 255, 0.4);
}

/* Confirmation Modal */
.confirmation-content {
    text-align: center;
    max-width: 500px;
}

.confirmation-header {
    padding: 40px 30px 20px;
}

.success-icon {
    font-size: 4rem;
    color: var(--success-color);
    margin-bottom: 20px;
    animation: successPulse 1s ease-in-out;
}

@keyframes successPulse {
    0% { transform: scale(0); }
    50% { transform: scale(1.2); }
    100% { transform: scale(1); }
}

.confirmation-header h3 {
    font-family: 'Orbitron', monospace;
    font-size: 1.8rem;
    color: var(--success-color);
    text-shadow: 0 0 10px var(--success-color);
}

.confirmation-body {
    padding: 20px 30px 40px;
}

#confirmation-details {
    background: rgba(0, 255, 0, 0.1);
    border: 1px solid var(--success-color);
    border-radius: 10px;
    padding: 20px;
    margin-bottom: 30px;
    text-align: left;
}

.btn-close-confirmation {
    background: var(--success-color);
    color: var(--dark-bg);
    border: none;
    padding: 12px 30px;
    border-radius: 8px;
    font-size: 1rem;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.3s ease;
    display: flex;
    align-items: center;
    gap: 8px;
    margin: 0 auto;
}

.btn-close-confirmation:hover {
    transform: translateY(-2px);
    box-shadow: 0 5px 20px rgba(0, 255, 0, 0.4);
}

/* Additional Animations and Effects */
.gaming-station::before {
    content: '';
    position: absolute;
    top: -2px;
    left: -2px;
    right: -2px;
    bottom: -2px;
    background: linear-gradient(45deg, transparent, currentColor, transparent);
    border-radius: 15px;
    opacity: 0;
    transition: opacity 0.3s ease;
    z-index: -1;
}

.gaming-station:hover::before {
    opacity: 0.3;
    animation: rotate 2s linear infinite;
}

@keyframes rotate {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

.gaming-station.reserved::after {
    content: '';
    position: absolute;
    top: 50%;
    left: 50%;
    width: 20px;
    height: 20px;
    background: var(--danger-color);
    border-radius: 50%;
    transform: translate(-50%, -50%);
    animation: reservedPulse 2s infinite;
}

@keyframes reservedPulse {
    0%, 100% { opacity: 0.5; transform: translate(-50%, -50%) scale(1); }
    50% { opacity: 1; transform: translate(-50%, -50%) scale(1.2); }
}

.gaming-station.in-use-soon::after {
    content: '';
    position: absolute;
    top: 5px;
    right: 5px;
    width: 8px;
    height: 8px;
    background: var(--warning-color);
    border-radius: 50%;
    animation: blink 1s infinite;
}

@keyframes blink {
    0%, 50% { opacity: 1; }
    51%, 100% { opacity: 0; }
}

/* Countdown Timer Animation */
.countdown-timer {
    position: absolute;
    top: -10px;
    right: -10px;
    width: 30px;
    height: 30px;
    background: var(--warning-color);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 0.7rem;
    font-weight: bold;
    color: var(--dark-bg);
    animation: countdownPulse 1s infinite;
}

@keyframes countdownPulse {
    0%, 100% { transform: scale(1); }
    50% { transform: scale(1.1); }
}

/* Loading Animation */
.loading-spinner {
    display: inline-block;
    width: 20px;
    height: 20px;
    border: 2px solid rgba(255, 255, 255, 0.3);
    border-radius: 50%;
    border-top-color: var(--primary-color);
    animation: spin 1s ease-in-out infinite;
}

@keyframes spin {
    to { transform: rotate(360deg); }
}

/* Particle Effects */
.particle {
    position: absolute;
    width: 4px;
    height: 4px;
    background: var(--primary-color);
    border-radius: 50%;
    pointer-events: none;
    animation: particleFloat 3s linear infinite;
}

@keyframes particleFloat {
    0% {
        transform: translateY(0) rotate(0deg);
        opacity: 1;
    }
    100% {
        transform: translateY(-100px) rotate(360deg);
        opacity: 0;
    }
}

/* Hover Effects for Navigation */
.nav-link {
    position: relative;
    overflow: hidden;
}

.nav-link::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(0, 255, 255, 0.2), transparent);
    transition: left 0.5s;
}

.nav-link:hover::before {
    left: 100%;
}

/* Enhanced Button Animations */
.cta-button {
    position: relative;
    overflow: hidden;
}

.cta-button::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
    transition: left 0.6s;
}

.cta-button:hover::before {
    left: 100%;
}

/* Form Input Focus Effects */
.form-group input:focus {
    animation: inputGlow 0.3s ease;
}

@keyframes inputGlow {
    0% { box-shadow: 0 0 5px rgba(0, 255, 255, 0.3); }
    50% { box-shadow: 0 0 20px rgba(0, 255, 255, 0.6); }
    100% { box-shadow: 0 0 10px rgba(0, 255, 255, 0.3); }
}

/* Status Bar Animation */
.status-bar {
    position: relative;
    overflow: hidden;
}

.status-bar::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 2px;
    background: linear-gradient(90deg, var(--primary-color), var(--secondary-color), var(--accent-color));
    animation: statusBarScan 3s linear infinite;
}

@keyframes statusBarScan {
    0% { left: -100%; }
    100% { left: 100%; }
}

/* Responsive Design */
@media (max-width: 768px) {
    .hero-title {
        font-size: 2.5rem;
    }

    .hero-stats {
        flex-direction: column;
        gap: 20px;
    }

    .nav-menu {
        display: none;
    }

    .hall-grid {
        grid-template-columns: 1fr;
    }

    .pc-grid {
        grid-template-columns: repeat(4, 1fr);
    }

    .form-row {
        grid-template-columns: 1fr;
    }

    .status-legend {
        flex-direction: column;
        align-items: center;
        gap: 15px;
    }
}

@media (max-width: 480px) {
    .hero-title {
        font-size: 2rem;
    }

    .pc-grid {
        grid-template-columns: repeat(3, 1fr);
    }

    .gaming-station {
        width: 60px;
        height: 60px;
    }

    .gaming-station i {
        font-size: 1.5rem;
    }

    .station-number {
        font-size: 0.7rem;
    }
}
